// Bullet point animations
.bullet-icon {
  position: relative;
  overflow: hidden;

  // Add a subtle glow effect
  &::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(var(--color-primary-rgb), 0.3) 0%, rgba(var(--color-primary-rgb), 0) 70%);
    opacity: 0;
    animation: pulse-glow 3s ease-in-out infinite;
  }

  // Rotate the icon slightly on hover
  &:hover i {
    animation: wiggle 0.5s ease-in-out;
  }
}

// Different icons for different sections
.feature-section:nth-of-type(3n+1) .bullet-icon i::before {
  content: "\f005"; /* fa-star */
}

.feature-section:nth-of-type(3n+2) .bullet-icon i::before {
  content: "\f0e7"; /* fa-bolt */
}

.feature-section:nth-of-type(3n+3) .bullet-icon i::before {
  content: "\f058"; /* fa-check-circle */
}

// Animations
@keyframes pulse-glow {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.5;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}

@keyframes wiggle {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(10deg);
  }
  75% {
    transform: rotate(-10deg);
  }
}
