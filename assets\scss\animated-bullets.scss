// Animated bullet points with FontAwesome icons
.bullet-icon {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(var(--color-primary-rgb), 0.2) 0%, rgba(var(--color-primary-rgb), 0) 70%);
    opacity: 0;
    transform: scale(0.5);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  i {
    position: relative;
    z-index: 2;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  // Different icons for different sections
  .feature-section:nth-of-type(3n+1) & i::before {
    content: "\f005"; /* fa-star */
  }
  
  .feature-section:nth-of-type(3n+2) & i::before {
    content: "\f0e7"; /* fa-bolt */
  }
  
  .feature-section:nth-of-type(3n+3) & i::before {
    content: "\f058"; /* fa-check-circle */
  }
}

// Hover effects
.bg-body:hover .bullet-icon,
.bg-darkmode-body:hover .bullet-icon {
  transform: scale(1.1);
  
  &::before {
    opacity: 1;
    transform: scale(1.5);
  }
  
  i {
    animation: pulse-icon 1s ease-in-out infinite;
  }
}

// Animations
@keyframes pulse-icon {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

// Different animations for different sections
.feature-section:nth-of-type(3n+1) .bullet-icon i {
  animation: star-animation 3s ease-in-out infinite;
}

.feature-section:nth-of-type(3n+2) .bullet-icon i {
  animation: bolt-animation 3s ease-in-out infinite;
}

.feature-section:nth-of-type(3n+3) .bullet-icon i {
  animation: check-animation 3s ease-in-out infinite;
}

@keyframes star-animation {
  0% {
    transform: scale(1) rotate(0deg);
  }
  25% {
    transform: scale(1.2) rotate(5deg);
  }
  50% {
    transform: scale(1) rotate(0deg);
  }
  75% {
    transform: scale(1.2) rotate(-5deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}

@keyframes bolt-animation {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  25% {
    transform: translateY(-3px);
    opacity: 0.8;
  }
  50% {
    transform: translateY(0);
    opacity: 1;
  }
  75% {
    transform: translateY(3px);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes check-animation {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
