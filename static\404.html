<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>404 - Page Not Found</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <style>
    /* Base styles */
    :root {
      --primary-color: #4ade80;
      --bg-color: #000;
      --text-color: #4ade80;
      --accent-color: #ff3e3e;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Courier New', monospace;
      background-color: var(--bg-color);
      color: var(--text-color);
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      overflow-x: hidden;
    }
    
    /* Matrix canvas */
    #matrix-canvas {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      opacity: 0.2;
    }
    
    /* Glitch overlay */
    .glitch-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: repeating-linear-gradient(
        0deg,
        rgba(0, 0, 0, 0.15),
        rgba(0, 0, 0, 0.15) 1px,
        transparent 1px,
        transparent 2px
      );
      pointer-events: none;
      z-index: 2;
    }
    
    /* Main content */
    .container {
      position: relative;
      z-index: 10;
      width: 100%;
      max-width: 1000px;
      margin: 0 auto;
      padding: 2rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
    }
    
    /* Terminal window */
    .terminal {
      width: 100%;
      max-width: 800px;
      background-color: rgba(0, 0, 0, 0.7);
      border-radius: 10px;
      overflow: hidden;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(74, 222, 128, 0.3);
      box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);
    }
    
    /* Terminal header */
    .terminal-header {
      background-color: #1a1a1a;
      padding: 0.75rem;
      display: flex;
      align-items: center;
      border-bottom: 1px solid rgba(74, 222, 128, 0.3);
    }
    
    .terminal-buttons {
      display: flex;
      gap: 0.5rem;
      margin-right: 1rem;
    }
    
    .terminal-button {
      width: 0.75rem;
      height: 0.75rem;
      border-radius: 50%;
    }
    
    .terminal-button.red { background-color: #ff5f56; }
    .terminal-button.yellow { background-color: #ffbd2e; }
    .terminal-button.green { background-color: #27c93f; }
    
    .terminal-title {
      flex-grow: 1;
      text-align: center;
      font-size: 0.875rem;
    }
    
    /* Terminal content */
    .terminal-content {
      padding: 1.5rem;
    }
    
    /* ASCII art */
    .ascii-art {
      font-size: 0.75rem;
      line-height: 1.2;
      white-space: pre;
      text-align: center;
      margin-bottom: 1.5rem;
      color: var(--accent-color);
      text-shadow: 0 0 5px rgba(255, 0, 0, 0.7);
      overflow-x: auto;
    }
    
    /* Error details */
    .error-details {
      margin-bottom: 1.5rem;
    }
    
    .error-line {
      display: flex;
      margin-bottom: 0.5rem;
    }
    
    .error-label {
      color: white;
      margin-right: 0.5rem;
      font-weight: bold;
    }
    
    .typing-effect {
      overflow: hidden;
      white-space: nowrap;
      border-right: 2px solid var(--primary-color);
      width: 0;
      animation: typing 2s steps(40, end) forwards;
    }
    
    .typing-effect-2 {
      overflow: hidden;
      white-space: nowrap;
      border-right: 2px solid var(--primary-color);
      width: 0;
      animation: typing 2s steps(40, end) forwards;
      animation-delay: 2s;
    }
    
    .typing-effect-3 {
      overflow: hidden;
      white-space: nowrap;
      border-right: 2px solid var(--primary-color);
      width: 0;
      animation: typing 2s steps(40, end) forwards;
      animation-delay: 4s;
    }
    
    @keyframes typing {
      from { width: 0 }
      to { width: 100% }
    }
    
    /* Interactive terminal */
    .terminal-interactive {
      background-color: rgba(0, 0, 0, 0.5);
      border: 1px solid rgba(74, 222, 128, 0.3);
      border-radius: 5px;
      padding: 1rem;
      margin-bottom: 1.5rem;
    }
    
    .terminal-prompt {
      margin-bottom: 0.5rem;
      color: #ffbd2e;
    }
    
    .terminal-options {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      margin-top: 1rem;
    }
    
    .terminal-option {
      width: 100%;
      text-align: left;
      padding: 0.5rem;
      background-color: rgba(74, 222, 128, 0.1);
      border: 1px solid rgba(74, 222, 128, 0.3);
      border-radius: 5px;
      color: var(--text-color);
      font-family: 'Courier New', monospace;
      cursor: pointer;
      transition: all 0.3s;
    }
    
    .terminal-option:hover {
      background-color: rgba(74, 222, 128, 0.2);
      box-shadow: 0 0 10px rgba(74, 222, 128, 0.3);
      text-shadow: 0 0 8px rgba(74, 222, 128, 0.8);
    }
    
    .option-label {
      color: #ffbd2e;
      margin-right: 0.5rem;
    }
    
    /* Response area */
    .response-area {
      display: none;
      background-color: rgba(0, 0, 0, 0.5);
      border: 1px solid rgba(74, 222, 128, 0.3);
      border-radius: 5px;
      padding: 1rem;
      margin-bottom: 1.5rem;
    }
    
    .response-title {
      margin-bottom: 0.5rem;
    }
    
    .response-title.success { color: #ffbd2e; }
    .response-title.error { color: var(--accent-color); }
    .response-title.info { color: var(--primary-color); }
    
    .response-message {
      color: white;
      margin-bottom: 1rem;
    }
    
    .response-actions {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
    }
    
    .response-action {
      text-align: center;
      padding: 0.5rem;
      background-color: rgba(74, 222, 128, 0.2);
      border: 1px solid rgba(74, 222, 128, 0.3);
      border-radius: 5px;
      color: var(--text-color);
      text-decoration: none;
      font-family: 'Courier New', monospace;
      cursor: pointer;
      transition: all 0.3s;
    }
    
    .response-action:hover {
      background-color: rgba(74, 222, 128, 0.3);
      box-shadow: 0 0 10px rgba(74, 222, 128, 0.3);
      text-shadow: 0 0 8px rgba(74, 222, 128, 0.8);
    }
    
    .response-action.danger {
      background-color: rgba(255, 62, 62, 0.2);
      border: 1px solid rgba(255, 62, 62, 0.3);
    }
    
    .response-action.danger:hover {
      background-color: rgba(255, 62, 62, 0.3);
      box-shadow: 0 0 10px rgba(255, 62, 62, 0.3);
    }
    
    /* Footer */
    .security-warning {
      text-align: center;
      margin-top: 2rem;
      font-size: 0.875rem;
      color: rgba(74, 222, 128, 0.7);
      max-width: 600px;
    }
    
    /* Responsive */
    @media (max-width: 768px) {
      .ascii-art {
        font-size: 0.6rem;
      }
      
      .response-actions {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <!-- Matrix-like background -->
  <canvas id="matrix-canvas"></canvas>
  
  <!-- Glitch overlay -->
  <div class="glitch-overlay"></div>
  
  <div class="container">
    <div class="terminal">
      <!-- Terminal header -->
      <div class="terminal-header">
        <div class="terminal-buttons">
          <div class="terminal-button red"></div>
          <div class="terminal-button yellow"></div>
          <div class="terminal-button green"></div>
        </div>
        <div class="terminal-title">system-error.sh - Terminal</div>
      </div>
      
      <!-- Terminal content -->
      <div class="terminal-content">
        <!-- Command prompt with typing effect -->
        <div class="command-prompt" style="margin-bottom: 1.5rem;">
          <div style="display: flex; align-items: flex-start; margin-bottom: 0.5rem;">
            <span style="margin-right: 0.5rem;">$</span>
            <div id="typed-output"></div>
          </div>
          <div id="typed-strings" style="display: none;">
            <p>ERROR 404: ACCESS_DENIED</p>
            <p>SECURITY BREACH DETECTED</p>
            <p>UNAUTHORIZED ACCESS ATTEMPT LOGGED</p>
            <p>INITIATING COUNTERMEASURES...</p>
          </div>
        </div>
        
        <!-- ASCII art -->
        <pre class="ascii-art">
  _  _    ___  _  _    ___  ____  ____  ___  ____    _  _  _  _  _  _ 
 / )( \  / __)/ )( \  / __)(  _ \(  _ \/ __)(  _ \  / )( \/ )( \/ )( \
 ) __ ( ( (__ ) __ ( ( (__  )   / )   /\__ \ )   /  \ \/ /\ \/ /\ \/ /
 \_)(_/  \___)\_)(_/  \___)(__\_)(__\_)(___/(__\_)   \__/  \__/  \__/ 
        </pre>
        
        <!-- Error details -->
        <div class="error-details">
          <div class="error-line">
            <span class="error-label">[ERROR]:</span>
            <span class="typing-effect">The requested resource could not be found on this server.</span>
          </div>
          <div class="error-line">
            <span class="error-label">[TRACE]:</span>
            <span class="typing-effect-2">Possible security breach detected. IP logged: <span id="user-ip">127.0.0.1</span></span>
          </div>
          <div class="error-line">
            <span class="error-label">[STATUS]:</span>
            <span class="typing-effect-3">Initiating defensive protocols...</span>
          </div>
        </div>
        
        <!-- Interactive terminal -->
        <div class="terminal-interactive">
          <div class="terminal-prompt">
            <span class="option-label">SYSTEM:</span> To continue, please select an option:
          </div>
          <div class="terminal-options">
            <button id="decrypt-btn" class="terminal-option">
              <span class="option-label">[1]</span> DECRYPT_LOCATION
            </button>
            <button id="override-btn" class="terminal-option">
              <span class="option-label">[2]</span> OVERRIDE_SECURITY
            </button>
            <button id="return-btn" class="terminal-option">
              <span class="option-label">[3]</span> RETURN_TO_SAFETY
            </button>
          </div>
        </div>
        
        <!-- Response areas -->
        <div id="decrypt-response" class="response-area">
          <div class="response-title success">DECRYPTION SUCCESSFUL:</div>
          <div class="response-message">You are currently trying to access a non-existent page. The system cannot find what you're looking for.</div>
          <div class="response-actions">
            <a href="/" class="response-action">
              <i class="fas fa-home"></i> Return Home
            </a>
            <button onclick="window.history.back()" class="response-action">
              <i class="fas fa-arrow-left"></i> Go Back
            </button>
          </div>
        </div>
        
        <div id="override-response" class="response-area">
          <div class="response-title error">ACCESS DENIED:</div>
          <div class="response-message">Security override attempt detected. This incident has been reported.</div>
          <div class="response-actions" style="grid-template-columns: 1fr;">
            <a href="/" class="response-action danger">
              <i class="fas fa-exclamation-triangle"></i> Emergency Exit
            </a>
          </div>
        </div>
        
        <div id="return-response" class="response-area">
          <div class="response-title info">SAFE ROUTE ESTABLISHED:</div>
          <div class="response-message">Redirecting to secure location...</div>
          <div class="response-actions">
            <a href="/cheatsheets/" class="response-action">
              <i class="fas fa-file-code"></i> Cheatsheets
            </a>
            <a href="/checklists/" class="response-action">
              <i class="fas fa-clipboard-check"></i> Checklists
            </a>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Security warning -->
    <div class="security-warning">
      <p style="margin-bottom: 0.5rem;">WARNING: This is a secure system. Unauthorized access is prohibited and may result in legal action.</p>
      <p>All activities on this system are logged and monitored.</p>
    </div>
  </div>
  
  <!-- Include Typed.js for terminal typing effect -->
  <script src="https://cdn.jsdelivr.net/npm/typed.js@2.0.12"></script>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Matrix background effect
      const canvas = document.getElementById('matrix-canvas');
      const ctx = canvas.getContext('2d');
      
      // Set canvas size
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      
      // Characters for matrix effect
      const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
      const charArray = chars.split('');
      
      const fontSize = 14;
      const columns = canvas.width / fontSize;
      
      // Array to track y position of each column
      const drops = [];
      for (let i = 0; i < columns; i++) {
        drops[i] = 1;
      }
      
      // Draw the matrix effect
      function drawMatrix() {
        // Semi-transparent black to create trail effect
        ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // Green text
        ctx.fillStyle = '#0f0';
        ctx.font = fontSize + 'px monospace';
        
        // Draw characters
        for (let i = 0; i < drops.length; i++) {
          // Random character
          const char = charArray[Math.floor(Math.random() * charArray.length)];
          
          // Draw character
          ctx.fillText(char, i * fontSize, drops[i] * fontSize);
          
          // Move to next position or reset to top
          if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
            drops[i] = 0;
          }
          
          drops[i]++;
        }
      }
      
      // Animation loop
      setInterval(drawMatrix, 50);
      
      // Typed.js effect
      if (typeof Typed !== 'undefined') {
        new Typed('#typed-output', {
          stringsElement: '#typed-strings',
          typeSpeed: 50,
          backSpeed: 30,
          backDelay: 1000,
          loop: false,
          showCursor: true,
          cursorChar: '█'
        });
      } else {
        // Fallback if Typed.js is not available
        document.getElementById('typed-output').textContent = 'ERROR 404: ACCESS_DENIED';
      }
      
      // Show user's IP (or a fake one for privacy)
      document.getElementById('user-ip').textContent = '192.168.' + Math.floor(Math.random() * 255) + '.' + Math.floor(Math.random() * 255);
      
      // Button interactions
      document.getElementById('decrypt-btn').addEventListener('click', function() {
        hideAllResponses();
        document.getElementById('decrypt-response').style.display = 'block';
      });
      
      document.getElementById('override-btn').addEventListener('click', function() {
        hideAllResponses();
        document.getElementById('override-response').style.display = 'block';
        
        // Add glitch effect on override attempt
        document.body.classList.add('glitch');
        setTimeout(() => {
          document.body.classList.remove('glitch');
        }, 1000);
      });
      
      document.getElementById('return-btn').addEventListener('click', function() {
        hideAllResponses();
        document.getElementById('return-response').style.display = 'block';
      });
      
      function hideAllResponses() {
        const responses = document.querySelectorAll('.response-area');
        responses.forEach(response => {
          response.style.display = 'none';
        });
      }
      
      // Add glitch effect class
      const style = document.createElement('style');
      style.textContent = `
        .glitch {
          animation: glitch 0.3s infinite;
        }
        
        @keyframes glitch {
          0% { transform: translate(0); }
          20% { transform: translate(-2px, 2px); }
          40% { transform: translate(-2px, -2px); }
          60% { transform: translate(2px, 2px); }
          80% { transform: translate(2px, -2px); }
          100% { transform: translate(0); }
        }
      `;
      document.head.appendChild(style);
    });
  </script>
</body>
</html>
