<!doctype html>
<html itemscope lang="en-us" itemtype="http://schema.org/WebPage">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=5">
  <title>404 - Page Not Found | <PERSON><PERSON><PERSON></title>
  <meta name="description" content="The page you are looking for could not be found.">
  <link rel="shortcut icon" href="/images/favicon.png" type="image/x-icon">
  <link rel="icon" href="/images/favicon.png" type="image/x-icon">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@200;300&family=Fraunces:wght@400;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="dark" style="background-color: #121212; color: #e0e0e0; margin: 0; padding: 0; font-family: 'Poppins', sans-serif;">
<style>
  /* Custom 404 page styles */
  .cyber-404 {
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
    min-height: 100vh;
    position: relative;
    overflow: hidden;
    margin: 0;
    padding: 2rem;
  }

  /* Matrix background */
  #matrix-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    opacity: 0.2;
  }

  /* Glitch overlay */
  .glitch-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
      0deg,
      rgba(0, 0, 0, 0.15),
      rgba(0, 0, 0, 0.15) 1px,
      transparent 1px,
      transparent 2px
    );
    pointer-events: none;
    z-index: 2;
  }

  /* Main content */
  .cyber-container {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
  }

  /* Terminal window */
  .cyber-terminal {
    width: 100%;
    max-width: 800px;
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(74, 222, 128, 0.3);
    box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
    animation: terminal-glow 2s ease-in-out infinite alternate;
  }

  @keyframes terminal-glow {
    from { box-shadow: 0 0 20px rgba(0, 255, 0, 0.3); }
    to { box-shadow: 0 0 30px rgba(0, 255, 0, 0.5); }
  }

  /* Terminal header */
  .cyber-terminal-header {
    background-color: #1a1a1a;
    padding: 0.75rem;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(74, 222, 128, 0.3);
  }

  .cyber-terminal-buttons {
    display: flex;
    gap: 0.5rem;
    margin-right: 1rem;
  }

  .cyber-terminal-button {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
  }

  .cyber-terminal-button.red { background-color: #ff5f56; }
  .cyber-terminal-button.yellow { background-color: #ffbd2e; }
  .cyber-terminal-button.green { background-color: #27c93f; }

  .cyber-terminal-title {
    flex-grow: 1;
    text-align: center;
    font-size: 0.875rem;
    color: #4ade80;
    font-family: 'Courier New', monospace;
  }

  /* Terminal content */
  .cyber-terminal-content {
    padding: 2rem;
    font-family: 'Courier New', monospace;
    color: #4ade80;
  }

  /* ASCII art */
  .cyber-ascii-art {
    font-size: 0.75rem;
    line-height: 1.2;
    white-space: pre;
    text-align: center;
    margin-bottom: 2rem;
    color: #ff3e3e;
    text-shadow: 0 0 5px rgba(255, 0, 0, 0.7);
    animation: ascii-flicker 1.5s infinite alternate;
  }

  @keyframes ascii-flicker {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
  }

  /* Error details */
  .cyber-error-details {
    margin-bottom: 2rem;
  }

  .cyber-error-line {
    display: flex;
    margin-bottom: 0.75rem;
    align-items: flex-start;
  }

  .cyber-error-label {
    color: #ff3e3e;
    margin-right: 0.5rem;
    font-weight: bold;
    min-width: 80px;
  }

  .cyber-typing-effect {
    overflow: hidden;
    white-space: nowrap;
    border-right: 2px solid #4ade80;
    animation: typing 2s steps(40, end) forwards, blink-caret 0.75s step-end infinite;
  }

  .cyber-typing-effect-2 {
    overflow: hidden;
    white-space: nowrap;
    border-right: 2px solid #4ade80;
    animation: typing 2.5s steps(50, end) 0.5s both, blink-caret 0.75s step-end infinite 0.5s;
  }

  .cyber-typing-effect-3 {
    overflow: hidden;
    white-space: nowrap;
    border-right: 2px solid #4ade80;
    animation: typing 2s steps(35, end) 1s both, blink-caret 0.75s step-end infinite 1s;
  }

  @keyframes typing {
    from { width: 0; }
    to { width: 100%; }
  }

  @keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: #4ade80; }
  }

  /* Action buttons */
  .cyber-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
  }

  .cyber-btn {
    background: rgba(74, 222, 128, 0.1);
    border: 1px solid #4ade80;
    color: #4ade80;
    padding: 0.75rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
  }

  .cyber-btn:hover {
    background: rgba(74, 222, 128, 0.2);
    box-shadow: 0 0 10px rgba(74, 222, 128, 0.4);
    transform: translateY(-2px);
    color: #4ade80;
    text-decoration: none;
  }

  .cyber-btn.danger {
    border-color: #ff3e3e;
    color: #ff3e3e;
    background: rgba(255, 62, 62, 0.1);
  }

  .cyber-btn.danger:hover {
    background: rgba(255, 62, 62, 0.2);
    box-shadow: 0 0 10px rgba(255, 62, 62, 0.4);
    color: #ff3e3e;
  }

  /* Security warning */
  .cyber-security-warning {
    text-align: center;
    margin-top: 2rem;
    font-size: 0.875rem;
    color: rgba(74, 222, 128, 0.7);
    max-width: 600px;
    font-family: 'Courier New', monospace;
  }

  /* Responsive */
  @media (max-width: 768px) {
    .cyber-ascii-art {
      font-size: 0.6rem;
    }

    .cyber-actions {
      grid-template-columns: 1fr;
    }
  }
</style>

<section class="cyber-404">
  <!-- Matrix-like background -->
  <canvas id="matrix-canvas"></canvas>

  <!-- Glitch overlay -->
  <div class="glitch-overlay"></div>

  <div class="cyber-container">
    <div class="cyber-terminal">
      <!-- Terminal header -->
      <div class="cyber-terminal-header">
        <div class="cyber-terminal-buttons">
          <div class="cyber-terminal-button red"></div>
          <div class="cyber-terminal-button yellow"></div>
          <div class="cyber-terminal-button green"></div>
        </div>
        <div class="cyber-terminal-title">system-error.sh - Terminal</div>
      </div>

      <!-- Terminal content -->
      <div class="cyber-terminal-content">
        <!-- ASCII art -->
        <pre class="cyber-ascii-art">
  _  _    ___  _  _    ___  ____  ____  ___  ____    _  _  _  _  _  _
 / )( \  / __)/ )( \  / __)(  _ \(  _ \/ __)(  _ \  / )( \/ )( \/ )( \
 ) __ ( ( (__ ) __ ( ( (__  )   / )   /\__ \ )   /  \ \/ /\ \/ /\ \/ /
 \_)(_/  \___)\_)(_/  \___)(__\_)(__\_)(___/(__\_)   \__/  \__/  \__/
        </pre>

        <!-- Error details -->
        <div class="cyber-error-details">
          <div class="cyber-error-line">
            <span class="cyber-error-label">[ERROR]:</span>
            <span class="cyber-typing-effect">The requested resource could not be found on this server.</span>
          </div>
          <div class="cyber-error-line">
            <span class="cyber-error-label">[TRACE]:</span>
            <span class="cyber-typing-effect-2">Possible security breach detected. IP logged: <span id="user-ip">127.0.0.1</span></span>
          </div>
          <div class="cyber-error-line">
            <span class="cyber-error-label">[STATUS]:</span>
            <span class="cyber-typing-effect-3">Initiating defensive protocols...</span>
          </div>
        </div>

        <!-- Action buttons -->
        <div class="cyber-actions">
          <a href="/" class="cyber-btn">
            <i class="fas fa-home"></i>
            RETURN TO BASE
          </a>
          <a href="/cheatsheets/" class="cyber-btn">
            <i class="fas fa-file-code"></i>
            ACCESS CHEATSHEETS
          </a>
          <a href="/checklists/" class="cyber-btn">
            <i class="fas fa-clipboard-check"></i>
            ACCESS CHECKLISTS
          </a>
          <button onclick="window.history.back()" class="cyber-btn danger">
            <i class="fas fa-arrow-left"></i>
            EMERGENCY RETREAT
          </button>
        </div>
      </div>
    </div>

    <!-- Security warning -->
    <div class="cyber-security-warning">
      <p style="margin-bottom: 0.5rem;">WARNING: This is a secure system. Unauthorized access is prohibited.</p>
      <p>All activities on this system are logged and monitored.</p>
    </div>
  </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Matrix background effect
  const canvas = document.getElementById('matrix-canvas');
  if (canvas) {
    const ctx = canvas.getContext('2d');

    // Set canvas size
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    // Characters for matrix effect
    const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
    const charArray = chars.split('');

    const fontSize = 14;
    const columns = canvas.width / fontSize;

    // Array to track y position of each column
    const drops = [];
    for (let i = 0; i < columns; i++) {
      drops[i] = 1;
    }

    // Draw the matrix effect
    function drawMatrix() {
      // Semi-transparent black to create trail effect
      ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Green text
      ctx.fillStyle = '#0f0';
      ctx.font = fontSize + 'px monospace';

      // Draw characters
      for (let i = 0; i < drops.length; i++) {
        // Random character
        const char = charArray[Math.floor(Math.random() * charArray.length)];

        // Draw character
        ctx.fillText(char, i * fontSize, drops[i] * fontSize);

        // Move to next position or reset to top
        if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
          drops[i] = 0;
        }

        drops[i]++;
      }
    }

    // Animation loop
    setInterval(drawMatrix, 50);

    // Resize canvas on window resize
    window.addEventListener('resize', () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    });
  }

  // Show user's IP (or a fake one for privacy)
  const ipElement = document.getElementById('user-ip');
  if (ipElement) {
    ipElement.textContent = '192.168.' + Math.floor(Math.random() * 255) + '.' + Math.floor(Math.random() * 255);
  }
});
</script>
</body>
</html>