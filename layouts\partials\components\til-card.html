<div class="bg-body dark:bg-darkmode-body rounded-lg shadow-md hover:shadow-lg transition-all duration-300 h-full border border-transparent hover:border-primary dark:hover:border-darkmode-primary">
  <div class="p-6">
    <!-- Date in a badge format -->
    <div class="mb-4 flex justify-between items-center">
      <span class="inline-block bg-primary/10 dark:bg-darkmode-primary/10 text-primary dark:text-darkmode-primary px-3 py-1 rounded-full text-sm">
        <i class="fa-regular fa-calendar-days mr-1"></i>
        {{ time.Format ":date_medium" .Date }}
      </span>

      <!-- TIL Category -->
      {{ with .Params.til_categories }}
        {{ range first 1 . }}
          <a href="{{ `til_categories/` | relLangURL }}{{ . | urlize | lower }}/"
             class="inline-block bg-theme-light dark:bg-darkmode-theme-light px-3 py-1 rounded-full text-sm hover:bg-primary hover:text-white dark:hover:bg-darkmode-primary dark:hover:text-white transition-colors">
            <i class="fa-solid fa-folder mr-1"></i>{{ . | humanize }}
          </a>
        {{ end }}
      {{ end }}
    </div>

    <!-- Title -->
    <h4 class="mb-3 text-xl font-bold group-hover:text-primary dark:group-hover:text-darkmode-primary transition-colors duration-300">
      <a href="{{ .RelPermalink }}" class="block hover:text-primary dark:hover:text-darkmode-primary">{{ .Title }}</a>
    </h4>

    <!-- Description -->
    <p class="mb-4 text-text dark:text-darkmode-text">{{ .Params.description }}</p>

    <!-- Tags -->
    {{ with .Params.til_tags }}
      <div class="mb-4 flex flex-wrap gap-2">
        {{ range . }}
          <a href="{{ `til_tags/` | relLangURL }}{{ . | urlize | lower }}/"
             class="inline-block bg-white dark:bg-darkmode-body px-2 py-1 text-xs rounded border border-border dark:border-darkmode-border hover:bg-primary hover:text-white dark:hover:bg-darkmode-primary dark:hover:text-white transition-colors">
            #{{ . }}
          </a>
        {{ end }}
      </div>
    {{ end }}

    <!-- Read More Button -->
    <a class="btn btn-outline-primary btn-sm" href="{{ .RelPermalink }}">
      {{ i18n "read_more" }}
    </a>
  </div>
</div>
