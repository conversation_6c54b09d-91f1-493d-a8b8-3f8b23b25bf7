/* Custom cursor styles - simple implementation */
html {
  --bg: hsl(132, 83%, 5%);
  --accent: hsl(84, 96%, 80%);
  --fsize: 16px;
  --circleMultiplier: 3;
  --circleSize: calc(calc(var(--circleMultiplier) * 1rem));
}

/* Light mode */
:root {
  --cursor-color: #121212;
}

/* Dark mode */
.dark {
  --cursor-color: #ffffff;
}

/* Hide default cursor */
body {
  cursor: none;
}

/* Main cursor element */
#circle {
  pointer-events: none;
  width: var(--circleSize);
  height: var(--circleSize);
  background-color: var(--cursor-color);
  border-radius: 50%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99999;
  translate: var(--xpos) var(--ypos);
  transform-origin: center center;
  transition:
    width .2s ease-in-out,
    height .2s ease-in-out,
    border-radius .4s ease-in-out,
    opacity .2s ease-in-out;
}

/* Hide cursor on touch devices */
@media (hover: none) and (pointer: coarse) {
  #circle {
    display: none;
  }

  body {
    cursor: auto !important;
  }

  *, *::before, *::after {
    cursor: auto !important;
  }
}

/* Link hover effect */
body:has(a:hover) #circle {
  --circleSize: calc(calc(var(--circleMultiplier) * 1.5rem));
  opacity: 0.2;
}

/* Image hover effect */
body:has(.gallery img:hover) #circle {
  opacity: 0.2;
}