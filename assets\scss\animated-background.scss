/* Animated Background Styles */

/* Base container for the animated background */
.animated-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  min-height: 800px;
  overflow: hidden;
  z-index: -1;
}

/* Light mode animated background */
.animated-background-light {
  background: linear-gradient(125deg, #f0f4ff 0%, #f9f9f9 40%, #edf7ff 100%);
}

/* Dark mode animated background */
.animated-background-dark {
  background: linear-gradient(125deg, #0f172a 0%, #1a1a2e 40%, #121212 100%);
}

/* Floating shapes */
.shape {
  position: absolute;
  opacity: 0.4;
  border-radius: 50%;
  filter: blur(5px);
  animation: float 15s infinite ease-in-out;
}

/* Different shapes for variety */
.shape-1 {
  width: 150px;
  height: 150px;
  background: var(--primary-color);
  top: 10%;
  left: 10%;
  animation-delay: 0s;
  animation-duration: 25s;
}

.shape-2 {
  width: 100px;
  height: 100px;
  background: var(--primary-color);
  top: 60%;
  left: 20%;
  animation-delay: 2s;
  animation-duration: 20s;
}

.shape-3 {
  width: 200px;
  height: 200px;
  background: var(--primary-color);
  top: 30%;
  right: 10%;
  animation-delay: 4s;
  animation-duration: 30s;
}

.shape-4 {
  width: 80px;
  height: 80px;
  background: var(--primary-color);
  bottom: 10%;
  right: 20%;
  animation-delay: 6s;
  animation-duration: 22s;
}

.shape-5 {
  width: 120px;
  height: 120px;
  background: var(--primary-color);
  bottom: 30%;
  left: 30%;
  animation-delay: 8s;
  animation-duration: 18s;
}

/* Grid pattern overlay */
.grid-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: 1;
}

.dark .grid-pattern {
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
}

/* Particle dots */
.particle {
  position: absolute;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: var(--primary-color);
  opacity: 0.5;
  animation: pulse 3s infinite ease-in-out;
}

.particle-1 {
  top: 15%;
  left: 25%;
  animation-delay: 0s;
}

.particle-2 {
  top: 35%;
  left: 65%;
  animation-delay: 0.5s;
}

.particle-3 {
  top: 65%;
  left: 15%;
  animation-delay: 1s;
}

.particle-4 {
  top: 75%;
  left: 75%;
  animation-delay: 1.5s;
}

.particle-5 {
  top: 45%;
  left: 45%;
  animation-delay: 2s;
}

.particle-6 {
  top: 25%;
  left: 85%;
  animation-delay: 2.5s;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-20px) translateX(10px);
  }
  50% {
    transform: translateY(0) translateX(20px);
  }
  75% {
    transform: translateY(20px) translateX(10px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.8);
    opacity: 0.8;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .shape {
    opacity: 0.15;
  }

  .shape-1, .shape-3 {
    width: 100px;
    height: 100px;
  }

  .shape-2, .shape-5 {
    width: 70px;
    height: 70px;
  }

  .shape-4 {
    width: 50px;
    height: 50px;
  }
}

/* Reduce motion preference */
@media (prefers-reduced-motion: reduce) {
  .shape, .particle {
    animation: none;
    opacity: 0.1;
  }
}
