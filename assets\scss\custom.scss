// DO NOT WRITE ANY STYLE IN THIS FILE
// If you want to add your own styles, please write it in the `./assets/scss/custom.scss` file.
@import url("https://fonts.googleapis.com/css2?family=Fraunces:ital,opsz,wght@0,9..144,100..900;1,9..144,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

html {
  scroll-behavior: smooth;
  /* Removed cursor: default to allow custom cursor to work */
}

/* Base link styles */
a {
  text-decoration: none;
  color: #007bff;
  position: relative;
  display: inline-block;
  font-weight: 600;
}

/* Remove decoration for all link states */
a:link,
a:visited,
a:hover,
a:active {
  text-decoration: none;
  font-weight: 600;
}

/* Exclude buttons, logos, and elements with no-after class */
a:not(.btn, .logo-dark a, .logo-light a, .no-after)::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 10px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 120 10' preserveAspectRatio='xMidYMid slice'%3E%3Cpath d='M0,5 C8,1 12,9 20,5 S32,1 40,5 S52,9 60,5 S72,1 80,5 S92,9 100,5 S112,1 120,5' stroke='%23007bff' stroke-width='2.5' fill='none' stroke-linecap='round' vector-effect='non-scaling-stroke'/%3E%3C/svg%3E");
  background-repeat: repeat-x;
  background-size: 120px 10px;
  background-position: 0 0;
  opacity: 0; /* Set opacity to 0 initially */
  transition: opacity 0.2s ease; /* Fade in smoothly */
}

/* Show and fade in the line */
a:not(.btn, .logo-dark a, .logo-light a, .no-after):hover::after {
  opacity: 1; /* Fade in the wavy line */
  animation: moveWave 2s linear infinite;
  animation-delay: 0.1s;
}

@keyframes moveWave {
  from {
    background-position: 0 0;
  }
  to {
    background-position: -120px 0;
  }
}

/* Logo styles - no animation */
.logo-dark a,
.logo-light a {
  text-decoration: none;
  color: #007bff;
  position: relative;
  display: inline-block;
}

// .landing{
//   background-image: url(/images/landing.png);
// }

/* Banner Styles */
.banner-slider {
  width: 100%;
  height: 100%;
}

.banner-slider .swiper-slide {
  height: 0;
  padding-bottom: 100%;
  position: relative;
  overflow: hidden;
}

.banner-slider .swiper-slide img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-slider .swiper-button-next,
.banner-slider .swiper-button-prev {
  width: 40px;
  height: 40px;
  background-color: var(--primary-color);
  border-radius: 50%;
  color: white;
}

.banner-slider .swiper-button-next:after,
.banner-slider .swiper-button-prev:after {
  font-size: 16px;
}

/* Content Styling */
.content-wrapper {
  line-height: 1.8;
}

.banner-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-dark);
}

.banner-subtitle {
  font-size: 1.25rem;
  color: var(--text-light);
  margin-bottom: 1.5rem;
}

.banner-content {
  font-size: 1.1rem;
  line-height: 1.8;
  color: var(--text-dark);
}

/* Bullet Points */
.banner-bullets li {
  line-height: 1.8;
  margin-bottom: 1rem;
}

.banner-bullets li span {
  line-height: 1.8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .banner .gallery-wrapper {
    max-width: 350px;
  }

  .banner-title {
    font-size: 2rem;
  }

  .banner-subtitle {
    font-size: 1.1rem;
  }

  .banner-bullets li {
    font-size: 0.95rem;
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 7px;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary dark:bg-darkmode-primary rounded-lg;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-darkmode-theme-light;
}

// abbr tag for phone
@media screen and (min-width: 0px) {
    abbr[data-title] {
        position: relative;

        /* ensure consistent styling across browsers */
        text-decoration: underline dotted;
    }

    abbr[data-title]:hover::after,
    abbr[data-title]:focus::after {
        content: attr(data-title);

        /* position tooltip like the native one */
        position: absolute;
        left: 0;
        bottom: -30px;
        width: auto;
        white-space: nowrap;

        /* style tooltip */
        background-color: #1e1e1e;
        color: #fff;
        border-radius: 3px;
        box-shadow: 1px 1px 5px 0 rgba(0, 0, 0, 0.4);
        font-size: 14px;
        padding: 3px 5px;
    }
}

/* Skills Section Styles */
.skill-card {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 240px;
}

.skill-card:hover {
  border-color: var(--primary-color);
}

.dark .skill-card:hover {
  border-color: var(--darkmode-primary-color);
}

.skill-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), transparent);
  transform: translateX(-100%);
  transition: transform 0.5s ease;
}

.dark .skill-card::before {
  background: linear-gradient(to right, var(--darkmode-primary-color), transparent);
}

.skill-card:hover::before {
  transform: translateX(0);
}

.skill-card .skill-topic {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  margin-bottom: 6px;
}

.skill-topic {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  animation: pulse 2s infinite;
}

.skill-card:hover .skill-topic {
  animation: none;
  opacity: 1;
}

@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

/* Skills Slider styles */
.skills-slider {
  overflow: visible !important;
  padding-bottom: 40px;
  max-width: 100%;
  margin: 0 auto;
  max-height: none !important;
}

.skills-slider .swiper-wrapper {
  align-items: center;
}

.skills-slider .swiper-slide {
  height: auto;
  opacity: 0.6;
  transform: scale(0.8);
  transition: all 0.4s ease;
  filter: blur(1px);
}

.skills-slider .swiper-slide-active {
  opacity: 1;
  transform: scale(1);
  filter: blur(0);
  z-index: 2;
}

.skills-slider .swiper-slide-prev,
.skills-slider .swiper-slide-next {
  opacity: 0.8;
  transform: scale(0.9);
  filter: blur(0);
  z-index: 1;
}

.skills-slider .swiper-button-next,
.skills-slider .swiper-button-prev {
  width: 40px;
  height: 40px;
  background-color: var(--primary-color);
  border-radius: 50%;
  color: white;
  margin-top: -30px;
  z-index: 10;
}

.dark .skills-slider .swiper-button-next,
.dark .skills-slider .swiper-button-prev {
  background-color: var(--darkmode-primary-color);
}

.skills-slider .swiper-button-next:after,
.skills-slider .swiper-button-prev:after {
  font-size: 16px;
}

.skills-slider-pagination {
  position: relative;
  width: 100%;
  bottom: 0;
  z-index: 10;
}

.skills-slider-pagination .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  background-color: #ddd;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.skills-slider-pagination .swiper-pagination-bullet-active {
  background-color: var(--primary-color);
  opacity: 1;
  width: 14px;
  height: 14px;
}

.dark .skills-slider-pagination .swiper-pagination-bullet-active {
  background-color: var(--darkmode-primary-color);
}

/* Fixed container styles to prevent overflow */
.container {
  overflow-x: hidden;
}

/* Responsive adjustments for skills slider */
@media (max-width: 767px) {
  .skills-slider .swiper-button-next,
  .skills-slider .swiper-button-prev {
    display: none;
  }

  .skills-slider .swiper-slide {
    max-width: 85%;
  }
}

@media (min-width: 768px) {
  .skills-slider {
    padding: 20px 50px;
  }

  .skills-slider .swiper-slide {
    max-width: 90%;
  }
}

@media (min-width: 1024px) {
  .skills-slider {
    padding: 30px 60px;
  }
}

/* Skills slider container */
.skills-slider-container {
  position: relative;
  overflow: hidden !important;
  width: 100%;
  margin: 0 auto;
  max-height: none !important;
}

/* Fix for container overflow */
/* Modify these rules to fix the dropdown issue */
.section,
.container:not(.navbar-container),
.row:not(.navbar-row),
.col-12:not(.navbar-col) {
  overflow-x: hidden;
}

/* Make sure dropdowns are visible */
.nav-dropdown {
  position: relative;
}

.nav-dropdown .dropdown-menu {
  position: absolute;
  z-index: 1000; /* Ensure it appears above other content */
  display: none;
}

.nav-dropdown.active .dropdown-menu {
  display: block;
}

/* Ensure nav items expand properly */
.navbar {
  overflow: visible !important;
}

.header-height-fix {
  overflow: visible !important;
}

/* Make all navigation elements visible regardless of overflow settings */
.navigation,
.navbar,
.navbar-container,
.navbar-row,
.navbar-wrapper,
.nav-item,
.nav-dropdown,
.dropdown-menu {
  overflow: visible !important;
}

/* Swiper specific overrides for skills */
.swiper-button-disabled {
  opacity: 0.4 !important;
  cursor: not-allowed;
}

/* Fix for coverflow effect */
.swiper-slide-shadow-left,
.swiper-slide-shadow-right {
  background-image: none !important;
}

/* Performance optimizations */
/* Only apply transitions to interactive elements */
a, button, .btn, .nav-item, .theme-switcher {
  transition: background-color 0.2s ease,
              color 0.2s ease,
              transform 0.2s ease,
              opacity 0.2s ease;
}

/* Minimal transitions for larger elements */
.card, .skill-card, .content-card, .project-card {
  transition: transform 0.2s ease,
              box-shadow 0.2s ease;
}

/* Optimize transitions for theme changes */
body, html, .dark, [class*="bg-"] {
  transition: background-color 0.2s ease,
              color 0.2s ease;
}

/* Disable animations for users who prefer reduced motion */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    transition-duration: 0.001ms !important;
    animation-duration: 0.001ms !important;
    animation-iteration-count: 1 !important;
    scroll-behavior: auto !important;
  }
}

/* Slide-in page transition effect */
.page-transition-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 9999;
  pointer-events: none;
  visibility: hidden;
  will-change: transform, opacity;
}

/* Beautiful inline code styling - clean and responsive */
:not(pre) > code {
  /* Light mode styling */
  background: #f1f3f4;
  color: #d73a49;
  padding: 0.2em 0.4em;
  border-radius: 4px;
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "Source Code Pro", "Consolas", "Liberation Mono", "Menlo", monospace;
  font-size: 0.875em;
  font-weight: 500;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  display: inline;
  margin: 0 0.1em;
  border: 1px solid #e1e4e8;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
  transition: all 0.15s ease;
  max-width: 100%;

  /* Responsive font sizing */
  @media (max-width: 768px) {
    font-size: 0.8em;
    padding: 0.15em 0.3em;
    margin: 0 0.05em;
  }

  @media (max-width: 480px) {
    font-size: 0.75em;
    padding: 0.1em 0.25em;
    white-space: pre-wrap;
    word-break: break-all;
  }

  /* Hover effect */
  &:hover {
    background: #e9ecef;
    border-color: #d0d7de;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  }
}

/* Dark mode styling */
.dark :not(pre) > code {
  background: #2d3748;
  color: #79b8ff;
  border: 1px solid #4a5568;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  max-width: 100%;

  /* Responsive font sizing for dark mode */
  @media (max-width: 768px) {
    font-size: 0.8em;
    padding: 0.15em 0.3em;
    margin: 0 0.05em;
  }

  @media (max-width: 480px) {
    font-size: 0.75em;
    padding: 0.1em 0.25em;
    white-space: pre-wrap;
    word-break: break-all;
  }

  /* Dark mode hover effect */
  &:hover {
    background: #4a5568;
    border-color: #718096;
    color: #9ecbff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
}

/* Override prose styles to use our clean responsive styling */
.prose :not(pre) > code {
  background: #f1f3f4 !important;
  color: #d73a49 !important;
  padding: 0.2em 0.4em !important;
  border-radius: 4px !important;
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "Source Code Pro", "Consolas", "Liberation Mono", "Menlo", monospace !important;
  font-size: 0.875em !important;
  font-weight: 500 !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
  overflow-wrap: break-word !important;
  border: 1px solid #e1e4e8 !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04) !important;
  transition: all 0.15s ease !important;
  max-width: 100% !important;

  /* Responsive font sizing for prose */
  @media (max-width: 768px) {
    font-size: 0.8em !important;
    padding: 0.15em 0.3em !important;
    margin: 0 0.05em !important;
  }

  @media (max-width: 480px) {
    font-size: 0.75em !important;
    padding: 0.1em 0.25em !important;
    white-space: pre-wrap !important;
    word-break: break-all !important;
  }

  &:hover {
    background: #e9ecef !important;
    border-color: #d0d7de !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08) !important;
  }
}

.dark .prose :not(pre) > code {
  background: #2d3748 !important;
  color: #79b8ff !important;
  border: 1px solid #4a5568 !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
  overflow-wrap: break-word !important;
  max-width: 100% !important;

  /* Responsive font sizing for dark mode prose */
  @media (max-width: 768px) {
    font-size: 0.8em !important;
    padding: 0.15em 0.3em !important;
    margin: 0 0.05em !important;
  }

  @media (max-width: 480px) {
    font-size: 0.75em !important;
    padding: 0.1em 0.25em !important;
    white-space: pre-wrap !important;
    word-break: break-all !important;
  }

  &:hover {
    background: #4a5568 !important;
    border-color: #718096 !important;
    color: #9ecbff !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
  }
}

/* Ensure no pseudo-elements add backticks */
code::before,
code::after {
  content: none !important;
  display: none !important;
}

.prose code::before,
.prose code::after {
  content: none !important;
  display: none !important;
}

/* Remove shadows from all images */
img {
  box-shadow: none !important;
  filter: none !important;
  border: none !important;
  outline: none !important;
}

.dark img {
  box-shadow: none !important;
  filter: none !important;
  border: none !important;
  outline: none !important;
}

/* Fix for feature images in both light and dark mode */
.feature-image-container {
  box-shadow: none !important;
  border: none !important;
  outline: none !important;
}

.feature-image-container img {
  box-shadow: none !important;
  filter: none !important;
  border: none !important;
  outline: none !important;
}

.dark .feature-image-container {
  box-shadow: none !important;
  border: none !important;
  outline: none !important;
}

.dark .feature-image-container img {
  box-shadow: none !important;
  filter: none !important;
  border: none !important;
  outline: none !important;
}

/* Remove all borders and outlines from images in dark mode */
.dark * img {
  border: none !important;
  outline: none !important;
}

/* Remove borders from all image containers in dark mode */
.dark .rounded-lg img,
.dark .rounded img,
.dark figure img,
.dark .content img,
.dark .card img,
.dark .post-content img {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Fix for inline code in paragraphs */
p code, li code, td code, h1 code, h2 code, h3 code, h4 code, h5 code, h6 code {
  display: inline-block;
  vertical-align: middle;
}



/* Slide-up transition */
.page-transition-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: #ffffff;
  transform: translateY(100%);
  will-change: transform;
}

.dark .page-transition-slide {
  background-color: #000000;
}

/* Optimize animations */
@media (prefers-reduced-motion: reduce) {
  .page-transition-container,
  .page-transition-fade {
    transition: none !important;
    animation: none !important;
  }
}

/* Hover effects for cards */
.card,
.skill-card,
.content-card,
.project-card {
  transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275),
              box-shadow 0.4s ease;
}

.card:hover,
.skill-card:hover,
.content-card:hover,
.project-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.dark .card:hover,
.dark .skill-card:hover,
.dark .content-card:hover,
.dark .project-card:hover {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

/* Button hover effects */
.btn {
  overflow: hidden;
  position: relative;
  z-index: 1;
  transition: all 0.4s ease;
}

.btn::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0;
  background-color: rgba(0, 0, 0, 0.1);
  transition: all 0.4s ease;
  z-index: -1;
}

.dark .btn::before {
  background-color: rgba(255, 255, 255, 0.1);
}

.btn:hover::before {
  height: 100%;
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

/* Feature CTA Button */
.btn-feature-cta {
  transition: all 0.5s ease;
}

.btn-feature-cta span.absolute {
  opacity: 0.2;
}

.btn-feature-cta:hover span.relative {
  z-index: 2;
  transition: all 0.3s ease;
}

.dark .btn-feature-cta span.absolute {
  opacity: 0.15;
}

/* Simplified navigation hover effect */
.nav-item {
  position: relative;
  transition: color 0.3s ease;
}

/* Removed the underline animation */

/* Image hover effects - removed for feature sections */
img:not(.logo-dark img, .logo-light img, .feature-image-container img) {
  transition: transform 0.5s ease, filter 0.5s ease;
}

img:not(.logo-dark img, .logo-light img, .feature-image-container img):hover {
  transform: scale(1.02);
  filter: brightness(1.05);
}

/* Feature section images - no effects */
.feature-image-container img {
  transition: none;
  box-shadow: none !important;
  filter: none !important;
}

.feature-image-container img:hover {
  transform: none;
  filter: none !important;
  box-shadow: none !important;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Input field animations */
input, textarea, select {
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

input:focus, textarea:focus, select:focus {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.dark input:focus,
.dark textarea:focus,
.dark select:focus {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Fixed navbar styles */
.navbar {
  background-color: var(--body-color);
  transition: transform 0.3s ease, background-color 0.3s ease;
  will-change: transform;
}

.dark .navbar {
  background-color: var(--body-color);
}

/* Navbar transition */
.navbar {
  transition: transform 0.3s ease, background-color 0.3s ease;
}
