<meta charset="utf-8" />

<!-- responsive meta -->
<meta
  name="viewport"
  content="width=device-width, initial-scale=1, maximum-scale=5"
/>

<!-- Performance optimizations -->
{{ if site.Params.optimization }}
  <!-- DNS prefetch and preconnect for faster resource loading -->
  <link rel="dns-prefetch" href="https://fonts.googleapis.com">
  <link rel="dns-prefetch" href="https://fonts.gstatic.com">

  {{ if site.Params.optimization.preconnectFonts }}
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  {{ end }}

  <!-- Preload critical assets -->
  <link rel="preload" href="{{ (resources.Get "scss/main.scss" | css.Sass (dict "outputStyle" "compressed") | fingerprint).RelPermalink }}" as="style">

  <!-- Instantpage for faster page navigation -->
  {{ if site.Params.optimization.instantPage }}
    <script src="https://instant.page/5.1.1" type="module" integrity="sha384-MWfCL6g1OTGsbSwfuMHc8+8J2u71/LA8dzlIN3ycajckxuZZmF+DNjdm7O6H3PSq"></script>
  {{ end }}
{{ end }}

<!-- asset bundles -->
{{ $assets := newScratch }}

{{ if .Site.Params.highlight.enableCodeCopy | default false }}
{{ $jsCode := resources.Get "js/code.js" }}
{{ $assets.Add "js" (slice $jsCode) }}
{{ end }}

{{ if $assets.Get "js" }}
{{ $bundleJS := $assets.Get "js" | resources.Concat "js/main.bundle.js" | resources.Minify | resources.Fingerprint "sha512" }}
<script defer type="text/javascript" id="script-bundle" src="{{ $bundleJS.RelPermalink }}" integrity="{{ $bundleJS.Data.Integrity }}" data-copy="{{ i18n "code.copy" }}" data-copied="{{ i18n "code.copied" }}"></script>
{{ end }}


<!-- favicon -->
{{ partialCached "favicon" . }}

<!-- manifest -->
{{ partialCached "manifest" . }}

<!-- site verifications -->
{{ partialCached "site-verifications.html" . }}

<!-- opengraph and twitter card -->
{{ partial "basic-seo.html" . }}

<!-- custom script -->
{{ partialCached "custom-script.html" . }}

<!-- google analytics -->
{{ if and site.Config.Services.GoogleAnalytics.ID (ne
site.Config.Services.GoogleAnalytics.ID "G-MEASUREMENT_ID") }} {{ template
"_internal/google_analytics.html" . }} {{ end }}

<!-- google tag manager -->
{{ partialCached "gtm.html" . }}

<!-- search index -->
{{ partial "search-index.html" . }}

<!-- matomo analytics -->
{{/* {{ partialCached "matomo-analytics.html" . }} */}}

<!--  Baidu analytics -->
{{/* {{ partialCached "baidu-analytics.html" . }} */}}

<!-- Plausible Analytics -->
{{/* {{ partialCached "plausible-analytics.html" . }} */}}

<!-- Counter Analytics -->
{{/* {{ partialCached "counter-analytics.html" . }} */}}

<!-- Crisp Chat -->
{{/* {{ partialCached "crisp-chat.html" . }} */}}

<!-- GSAP for animations -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.3/gsap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.3/ScrollTrigger.min.js"></script>

<!-- AOS (Animate on Scroll) Library -->
<link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
<script src="https://unpkg.com/aos@next/dist/aos.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true,
      mirror: false
    });
  });
</script>

<!-- Ensure CSS variables are properly defined for cursor -->
<style>
:root {
  --primary-color: #121212;
  --primary-color-rgb: 18, 18, 18;
}

.dark {
  --primary-color: #ffffff;
  --primary-color-rgb: 255, 255, 255;
}
</style>
