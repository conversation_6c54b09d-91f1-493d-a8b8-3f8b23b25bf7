<!-- Section-Specific Search Component -->
<!--
  Parameters:
  - section: The section to search in (e.g., "blog", "til", "cheatsheets", "checklists")
  - placeholder: Placeholder text for the search input
-->

{{ $section := .section }}
{{ $placeholder := .placeholder | default (printf "Search %s..." $section) }}
{{ $inputId := printf "%s-search-input" $section }}
{{ $btnId := printf "%s-search-btn" $section }}
{{ $helpId := printf "%s-search-help" $section }}
{{ $functionName := printf "search_%s" $section }}

<div class="bg-theme-light dark:bg-darkmode-theme-light rounded p-4">
  <div class="relative">
    <input type="text" id="{{ $inputId }}" placeholder="{{ $placeholder }}"
           class="w-full px-4 py-2 rounded-lg border border-border dark:border-darkmode-border bg-white dark:bg-darkmode-body text-text dark:text-darkmode-text focus:outline-none focus:ring-1 focus:ring-primary dark:focus:ring-darkmode-primary">
    <i class="fas fa-search absolute right-3 top-3 text-text/50 dark:text-darkmode-text/50"></i>
  </div>
  <div class="flex mt-2 gap-2">
    <button id="{{ $btnId }}" class="flex-1 bg-primary text-white dark:bg-darkmode-primary dark:text-darkmode-body px-4 py-2 rounded-lg hover:opacity-90 transition-opacity">
      <i class="fas fa-search mr-1"></i> Search
    </button>
    <button id="{{ $helpId }}" class="bg-theme-dark/10 dark:bg-darkmode-theme-dark/10 text-text dark:text-darkmode-text px-3 py-2 rounded-lg hover:bg-theme-dark/20 dark:hover:bg-darkmode-theme-dark/20 transition-colors" title="Search Help">
      <i class="fas fa-question"></i>
    </button>
  </div>

  <!-- Advanced Search Help -->
  <div id="{{ $helpId }}-content" class="hidden mt-3 text-sm bg-white dark:bg-darkmode-body p-3 rounded-lg border border-border dark:border-darkmode-border">
    <h6 class="font-bold mb-2">Advanced Search Operators:</h6>
    <ul class="space-y-1 text-xs">
      <li><code class="bg-theme-light dark:bg-darkmode-theme-light px-1 py-0.5 rounded">#ch keyword</code> - Search only in cheatsheets</li>
      <li><code class="bg-theme-light dark:bg-darkmode-theme-light px-1 py-0.5 rounded">#b keyword</code> - Search only in blog</li>
      <li><code class="bg-theme-light dark:bg-darkmode-theme-light px-1 py-0.5 rounded">#cl keyword</code> - Search only in checklists</li>
      <li><code class="bg-theme-light dark:bg-darkmode-theme-light px-1 py-0.5 rounded">#til keyword</code> - Search only in TIL</li>
      <li><code class="bg-theme-light dark:bg-darkmode-theme-light px-1 py-0.5 rounded">#p keyword</code> - Search only in projects</li>
      <li><code class="bg-theme-light dark:bg-darkmode-theme-light px-1 py-0.5 rounded">-b keyword</code> - Exclude blog from search</li>
      <li><code class="bg-theme-light dark:bg-darkmode-theme-light px-1 py-0.5 rounded">keyword1 & keyword2</code> - Must contain both</li>
      <li><code class="bg-theme-light dark:bg-darkmode-theme-light px-1 py-0.5 rounded">keyword1 | keyword2</code> - Either one</li>
    </ul>
  </div>

  <script>
    // Handle Enter key press
    document.getElementById('{{ $inputId }}').addEventListener('keyup', function(e) {
      if (e.key === 'Enter') {
        searchFunction();
      }
    });

    // Handle search button click
    document.getElementById('{{ $btnId }}').addEventListener('click', function() {
      searchFunction();
    });

    // Handle help button click
    document.getElementById('{{ $helpId }}').addEventListener('click', function() {
      const helpContent = document.getElementById('{{ $helpId }}-content');
      helpContent.classList.toggle('hidden');
    });

    function searchFunction() {
      const searchInput = document.getElementById('{{ $inputId }}');
      let query = searchInput.value;
      let sectionParam = '&section={{ $section }}';

      // Process advanced search operators
      if (query.includes('#ch ')) {
        query = query.replace('#ch ', '');
        sectionParam = '&section=cheatsheets';
      } else if (query.includes('#b ')) {
        query = query.replace('#b ', '');
        sectionParam = '&section=blog';
      } else if (query.includes('#cl ')) {
        query = query.replace('#cl ', '');
        sectionParam = '&section=checklists';
      } else if (query.includes('#til ')) {
        query = query.replace('#til ', '');
        sectionParam = '&section=til';
      } else if (query.includes('#p ')) {
        query = query.replace('#p ', '');
        sectionParam = '&section=projects';
      } else if (query.includes('-b ')) {
        query = query.replace('-b ', '');
        sectionParam = '&section=cheatsheets,checklists,til,projects';
      } else if (query.includes('-ch ')) {
        query = query.replace('-ch ', '');
        sectionParam = '&section=blog,checklists,til,projects';
      } else if (query.includes('-cl ')) {
        query = query.replace('-cl ', '');
        sectionParam = '&section=blog,cheatsheets,til,projects';
      } else if (query.includes('-til ')) {
        query = query.replace('-til ', '');
        sectionParam = '&section=blog,cheatsheets,checklists,projects';
      } else if (query.includes('-p ')) {
        query = query.replace('-p ', '');
        sectionParam = '&section=blog,cheatsheets,checklists,til';
      }

      window.location.href = '/search/?q=' + encodeURIComponent(query) + sectionParam;
    }
  </script>
</div>
