#################### default parameters ################################
# favicon
favicon = "images/favicon.png"
# logo
logo = "images/logo.png"
logo_darkmode = "images/logo-darkmode.png"
# use `px` or `x` with logo_width, example: "100px".
# Note: logo_width is not work with .svg file
logo_width = "125px"
logo_height = "24px"
# if logo_webp set false, will not generate WEBP version of logo | default is true
logo_webp = true
# logo text will only show when logo is missing.
logo_text = "Suhesh"
# navbar fixed to top
navbar_fixed = true
# theme-mode
theme_switcher = true
theme_default = "dark" # available options [light/dark/system]
# Main Sections
mainSections = ["blog", "til"]

# contact form action
contact_form_action = "https://formspree.io/f/mrgwjvry" # contact form works with [https://airform.io/] or [https://formspree.io]
# google tag manager, see https://developers.google.com/tag-manager/
google_tag_manager = "" # example: G-XXXXXXXXXX
google_adsense = ""     # example: ca-pub-xxxxxxxxxxxxxxxx
# custom script on header, example: custom_script= "<script>console.log(\"Hello World\")</script>"
custom_script = ""
# copyright
copyright = "Copyright &copy; 2023 [Suhesh Kasti](https://t.me/suheshkasti)"

# Preloader
# preloader module: https://github.com/gethugothemes/hugo-modules/tree/master/components/preloader
[preloader]
enable = false
preloader = "images/preloader.gif" # use jpg, png, svg or gif format.

# Navigation button
[navigation_button]
enable = true
label = "get a quote"
link = "contact"

# search
# search module: https://github.com/gethugothemes/hugo-modules/tree/master/search
[search]
enable = true
primary_color = "#121212"
include_sections = ["blog", "projects", "checklists", "cheatsheets", "til"]
show_image = true
show_description = true
show_tags = true
show_categories = true

# highlight
# hugo built-in highlight.js
[highlight]
enableCodeCopy = true

# seo meta data for OpenGraph / Twitter Card
# seo module: https://github.com/gethugothemes/hugo-modules/tree/master/seo-tools/basic-seo
[metadata]
keywords = ["Suhesh", "Kasti", "Suhesh Kasti", "Suhesh-Kasti", "Kasti Suhesh", "Kasti-Suhesh"]
description = "Portfolio website of Suhesh Kasti"
author = "Suhesh Kasti"
image = "images/avatar.png"

# site verifications
# verification module: https://github.com/gethugothemes/hugo-modules/tree/master/seo-tools/site-verifications
[site_verification]
google = ""   # Your verification code
bing = ""     # Your verification code
baidu = ""    # Your verification code
facebook = "" # Your verification code
mastodon = "" # Your verification code

# cookies
# cookies module: https://github.com/gethugothemes/hugo-modules/tree/master/components/cookie-consent
[cookies]
enable = false
expire_days = 60
content = "This site uses cookies. By continuing to use this website, you agree to their use."
button = "I Accept"

######################## sidebar widgets #########################
[widgets]
sidebar = ["categories", "tags"]

# google map
[google_map]
enable = false
map_api_key = "AIzaSyCcABaamniA6OL5YvYSpB3pFMNrXwXnLwU"
map_latitude = "51.5223477"
map_longitude = "-0.1622023"
map_marker = "images/marker.png"


# Subscription
[subscription]
enable = false
# mailchimp subsciption
mailchimp_form_action = "https://gmail.us4.list-manage.com/subscribe/post?u=463ee871f45d2d93748e77cad&amp;id=a0a2c6d074" # replace this url with yours
mailchimp_form_name = "b_463ee871f45d2d93748e77cad_a0a2c6d074"
