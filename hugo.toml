######################## default configuration ####################
# The base URL of your site (required). This will be prepended to all relative URLs.
baseURL = "/"
# Title of your website (required).
title = "<PERSON><PERSON><PERSON>"
# Your theme name
timeZone = "Asia/Kathmandu"
# post pagination
[pagination]
  pagerSize = 6   # see https://gohugo.io/extras/pagination/
# post excerpt
summaryLength = 10 # see https://gohugo.io/content-management/excerpts/
# google analytics
googleAnalytics = "G-MEASUREMENT_ID" # see https://gohugo.io/templates/internal/#configure-google-analytics
# disqus short name
[services]
  [services.disqus]
    shortname = "suhesh-pages-dev" # we use disqus to show comments in blog posts . To install disqus please follow this tutorial https://portfolio.peter-baumgartner.net/2017/09/10/how-to-install-disqus-on-hugo/
# disable language
disableLanguages = [] # example: ["fr"] for disable french language. see https://gohugo.io/content-management/multilingual/
hasCJKLanguage = false #  If hasCJKLanguage true, auto-detect Chinese/Japanese/Korean Languages in the content. see: https://gohugo.io/getting-started/configuration/#hascjklanguage

########################## Taxonomies ############################
[taxonomies]
category = "categories"
tag = "tags"
project_category = "project_categories"
project_tag = "project_tags"
cheatsheet_category = "cheatsheet_categories"
cheatsheet_tag = "cheatsheet_tags"
checklist_category = "checklist_categories"
checklist_tag = "checklist_tags"
til_category = "til_categories"
til_tag = "til_tags"
folder = "folders"

########################## Permalinks ############################
[permalinks.page]
"pages" = "/:slugorcontentbasename/"


############################# Modules ############################
[module]
# Mount our layouts directory first to override theme templates
[[module.mounts]]
source = "layouts"
target = "layouts"
weight = 1

[[module.mounts]]
source = "assets"
target = "assets"

[[module.mounts]]
source = "hugo_stats.json"
target = "assets/watching/hugo_stats.json"

############################# Build ##############################
[build]
noJSConfigInAssets = false
useResourceCacheWhen = 'fallback'
writeStats = true
[build.buildStats]
enable = true
[[build.cachebusters]]
source = 'assets/watching/hugo_stats\.json'
target = 'style\.css'
[[build.cachebusters]]
source = '(postcss|tailwind)\.config\.js'
target = 'css'
[[build.cachebusters]]
source = 'assets/.*\.(js|ts|jsx|tsx)'
target = 'js'
[[build.cachebusters]]
source = 'assets/.*\.(css|scss|sass)'
target = 'css'
[[build.cachebusters]]
source = 'data/.*\.(.*)$'
target = 'css'
[[build.cachebusters]]
source = 'assets/.*\.(.*)$'
target = '$1'
[security]
  [security.funcs]
    getenv = ['^HUGO_', '^NODE_']

############################# Outputs ############################
[outputs]
home = ["HTML", "RSS", "WebAppManifest", "SearchIndex"]
page = ["HTML"]
section = ["HTML", "RSS"]

############################# Imaging ############################
[imaging]
# See https://github.com/disintegration/imaging
# Default JPEG or WebP quality setting. Default is 75.
quality = 90
resampleFilter = "Lanczos"

############################ Minify ##############################
[minify]
  disableCSS = false
  disableHTML = false
  disableJS = false
  disableJSON = false
  disableSVG = false
  disableXML = false
  minifyOutput = true
  [minify.tdewolff]
    [minify.tdewolff.css]
      keepCSS2 = true
      precision = 0
    [minify.tdewolff.html]
      keepComments = false
      keepConditionalComments = true
      keepDefaultAttrVals = true
      keepDocumentTags = true
      keepEndTags = true
      keepQuotes = false
      keepWhitespace = false
    [minify.tdewolff.js]
      keepVarNames = false
      precision = 0
    [minify.tdewolff.json]
      precision = 0
    [minify.tdewolff.svg]
      precision = 0
    [minify.tdewolff.xml]
      keepWhitespace = false

############################ Caches ##############################
[caches]
  [caches.images]
  dir = ":resourceDir/_gen"
  maxAge = "720h"

  [caches.assets]
  dir = ":resourceDir/_gen"
  maxAge = "720h"

  # New caches for better performance
  [caches.getcsv]
  dir = ":cacheDir/:project"
  maxAge = "24h"

  [caches.getjson]
  dir = ":cacheDir/:project"
  maxAge = "24h"

  [caches.getresource]
  dir = ":cacheDir/:project"
  maxAge = "24h"

  [caches.modules]
  dir = ":cacheDir/modules"
  maxAge = "720h"

############################ Markup ##############################
[markup]
[markup.goldmark.renderer]
unsafe = true

[markup.highlight]
style = 'dracula' # see https://xyproto.github.io/splash/docs/all.html

[markup.tableOfContents]
startLevel = 1
endLevel = 6
ordered = false


########################### Media types ###########################
[mediaTypes]
[mediaTypes."application/manifest+json"]
suffixes = ["webmanifest"]

[singlePages]
[singlePages.readTime]
enable = true
content = "min read"

[singlePages.scrollprogress]
enable = true

########################### Output Format ##########################
[outputFormats]
[outputFormats.WebAppManifest]
mediaType = "application/manifest+json"
rel = "manifest"

[outputFormats.SearchIndex]
mediaType = "application/json"
baseName = "searchindex"
isPlainText = true
notAlternative = true


############################# Plugins ##############################

# CSS Plugins
[[params.plugins.css]]
link = "plugins/swiper/swiper-bundle.css"
lazy = true
attributes = "media='all'"

[[params.plugins.css]]
link = "plugins/glightbox/glightbox.css"
lazy = true
attributes = "media='all'"

[[params.plugins.css]]
link = "plugins/font-awesome/v6/brands.css"
lazy = true
attributes = "media='all'"

[[params.plugins.css]]
link = "plugins/font-awesome/v6/solid.css"
lazy = true
attributes = "media='all'"

[[params.plugins.css]]
link = "plugins/font-awesome/v6/icons.css"
lazy = true
attributes = "media='all'"

# JS Plugins
[[params.plugins.js]]
link = "js/search.js"
lazy = false
defer = true

[[params.plugins.js]]
link = "plugins/swiper/swiper-bundle.js"
lazy = false
defer = true

[[params.plugins.js]]
link = "plugins/glightbox/glightbox.js"
lazy = true
defer = true

[[params.plugins.js]]
link = "js/gallery-slider.js"
lazy = true
defer = true

[[params.plugins.js]]
link = "js/accordion.js"
lazy = true
defer = true

[[params.plugins.js]]
link = "js/tab.js"
lazy = true
defer = true

[[params.plugins.js]]
link = "js/modal.js"
lazy = true
defer = true

[[params.plugins.js]]
link = "plugins/cookie.js"
lazy = true
defer = true

[[params.plugins.js]]
link = "plugins/youtube-lite.js"
lazy = true
defer = true

########################## Optimization Settings ###################
[params.optimization]
  lazyLoading = true
  preconnectFonts = true
  deferJS = true
  asyncCSS = true
  minifyJSON = true
  instantPage = true
