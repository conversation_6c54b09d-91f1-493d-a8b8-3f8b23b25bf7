{{ define "main" }} {{ partial "page-header" . }}
<section class="section">
  <div class="container">
    <!-- Introduction Section -->
    <div class="text-center max-w-3xl mx-auto mb-16">
      <h1 class="h2 mb-6">Cheatsheets Library</h1>
      <p class="text-lg mb-8">Access our comprehensive collection of cheatsheets organized by topics. These quick reference guides will help you master various technologies and techniques.</p>
      <div class="flex justify-center space-x-4">
        <a href="/cheatsheet_categories/" class="btn btn-primary">Browse Categories</a>
        <a href="#cheatsheets" class="btn btn-outline-primary">View All Cheatsheets</a>
      </div>


    </div>

    <!-- Display folders first -->
    <div id="categories" class="mb-20">
      <div class="flex items-center mb-10">
        <div class="h-px bg-border dark:bg-darkmode-border flex-grow"></div>
        <h2 class="text-2xl font-bold px-6">Categories</h2>
        <div class="h-px bg-border dark:bg-darkmode-border flex-grow"></div>
      </div>
      <div class="row mb-16">
        <!-- DevOps Folder -->
        <div class="md:col-6 lg:col-4 mb-8">
          <div class="bg-theme-light dark:bg-darkmode-theme-light rounded-lg p-8 text-center hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-2 border-primary dark:border-darkmode-primary group">
            <div class="flex items-center justify-center mb-6">
              <div class="w-20 h-20 flex items-center justify-center rounded-full bg-primary bg-opacity-10 dark:bg-opacity-20 group-hover:bg-opacity-20 dark:group-hover:bg-opacity-30 transition-all duration-300">
                <i class="fas fa-folder-open text-4xl text-primary dark:text-darkmode-primary"></i>
              </div>
            </div>
            <h4 class="mb-3 text-xl font-bold group-hover:text-primary dark:group-hover:text-darkmode-primary transition-colors duration-300">
              <a href="/cheatsheets/devops/" class="block">DevOps</a>
            </h4>
            <p class="mb-4 text-text dark:text-darkmode-text">Cheatsheets for DevOps tools and technologies</p>
            <div class="mt-6">
              <a href="/cheatsheets/devops/" class="inline-flex items-center text-primary dark:text-darkmode-primary hover:underline">
                <span>Browse Contents</span>
                <i class="fas fa-arrow-right ml-2 text-sm"></i>
              </a>
            </div>
          </div>
        </div>

        <!-- SQLInjection Folder -->
        <div class="md:col-6 lg:col-4 mb-8">
          <div class="bg-theme-light dark:bg-darkmode-theme-light rounded-lg p-8 text-center hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-2 border-primary dark:border-darkmode-primary group">
            <div class="flex items-center justify-center mb-6">
              <div class="w-20 h-20 flex items-center justify-center rounded-full bg-primary bg-opacity-10 dark:bg-opacity-20 group-hover:bg-opacity-20 dark:group-hover:bg-opacity-30 transition-all duration-300">
                <i class="fas fa-folder-open text-4xl text-primary dark:text-darkmode-primary"></i>
              </div>
            </div>
            <h4 class="mb-3 text-xl font-bold group-hover:text-primary dark:group-hover:text-darkmode-primary transition-colors duration-300">
              <a href="/cheatsheets/sqlinjection/" class="block">SQL Injection</a>
            </h4>
            <p class="mb-4 text-text dark:text-darkmode-text">Cheatsheets for SQL Injection techniques</p>
            <div class="mt-6">
              <a href="/cheatsheets/sqlinjection/" class="inline-flex items-center text-primary dark:text-darkmode-primary hover:underline">
                <span>Browse Contents</span>
                <i class="fas fa-arrow-right ml-2 text-sm"></i>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Display regular pages (not folders) -->
    {{ $regularPages := where .RegularPages "Params.folder" "eq" nil }}
    {{ if $regularPages }}
    <div id="cheatsheets">
      <div class="flex items-center mb-10">
        <div class="h-px bg-border dark:bg-darkmode-border flex-grow"></div>
        <h2 class="text-2xl font-bold px-6">All Cheatsheets</h2>
        <div class="h-px bg-border dark:bg-darkmode-border flex-grow"></div>
      </div>
      <div class="row">
        {{ range $regularPages }}
        <div class="md:col-6 lg:col-4 mb-14">
          {{ partial "components/cheatsheets" . }}
        </div>
        {{ end }}
      </div>
    </div>
    {{ end }}

    <!-- Explore Other Content Section -->
    <div class="mt-16">
      <div class="flex items-center mb-10">
        <div class="h-px bg-border dark:bg-darkmode-border flex-grow"></div>
        <h2 class="text-2xl font-bold px-6">Explore Other Content</h2>
        <div class="h-px bg-border dark:bg-darkmode-border flex-grow"></div>
      </div>
      <div class="row">
        <div class="col-12">
          {{ partial "components/explore-other-content-horizontal.html" (dict "currentSection" "cheatsheets") }}
        </div>
      </div>
    </div>
  </div>
</section>
{{ end }}
