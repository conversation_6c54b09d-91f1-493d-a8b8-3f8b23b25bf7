{{ define "main" }}
{{ partial "page-header" . }}

<section class="section">
  <div class="container">
    <!-- Header Section -->
    <div class="text-center max-w-4xl mx-auto mb-16">
      <div class="mb-6">
        <div class="inline-flex items-center justify-center w-20 h-20 bg-primary/10 dark:bg-darkmode-primary/10 rounded-full mb-4">
          <i class="fas fa-lightbulb text-3xl text-primary dark:text-darkmode-primary"></i>
        </div>
      </div>
      <h1 class="h1 mb-6 bg-gradient-to-r from-primary to-primary/70 dark:from-darkmode-primary dark:to-darkmode-primary/70 bg-clip-text text-transparent">
        {{ .Title }}
      </h1>
      <p class="text-xl mb-8 text-text dark:text-darkmode-text leading-relaxed">
        Explore {{ len .Data.Pages }} TIL {{ if eq (len .Data.Pages) 1 }}entry{{ else }}entries{{ end }} in the "{{ .Title }}" category.
        Discover insights and learnings in this domain.
      </p>
      <div class="flex justify-center space-x-4">
        <a href="/til_categories/" class="btn btn-outline-primary">
          <i class="fas fa-folder mr-2"></i>All Categories
        </a>
        <a href="/til/" class="btn btn-primary">
          <i class="fas fa-arrow-left mr-2"></i>Back to TIL
        </a>
      </div>
    </div>

    <!-- Stats Bar -->
    <div class="bg-theme-light dark:bg-darkmode-theme-light rounded-xl p-6 mb-12 border border-border dark:border-darkmode-border">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
        <div>
          <div class="text-2xl font-bold text-primary dark:text-darkmode-primary mb-1">{{ len .Data.Pages }}</div>
          <div class="text-text dark:text-darkmode-text text-sm">{{ if eq (len .Data.Pages) 1 }}Entry{{ else }}Entries{{ end }}</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-primary dark:text-darkmode-primary mb-1">{{ .Title }}</div>
          <div class="text-text dark:text-darkmode-text text-sm">Category</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-primary dark:text-darkmode-primary mb-1">TIL</div>
          <div class="text-text dark:text-darkmode-text text-sm">Content Type</div>
        </div>
      </div>
    </div>

    <!-- Category Filter -->
    {{ if gt (len site.Taxonomies.til_categories) 1 }}
    <div class="mb-12">
      <div class="text-center mb-6">
        <h2 class="text-xl font-bold text-dark dark:text-darkmode-dark mb-2">Browse Categories</h2>
        <p class="text-text dark:text-darkmode-text text-sm">Switch between different TIL categories</p>
      </div>

      <div class="bg-theme-light dark:bg-darkmode-theme-light rounded-xl p-6 border border-border dark:border-darkmode-border">
        <div class="flex flex-wrap justify-center gap-3">
          {{ range $name, $items := site.Taxonomies.til_categories }}
            <a href="{{ .Page.RelPermalink }}"
               class="inline-flex items-center px-4 py-2 rounded-full {{ if eq $.Data.Term .Page.Title }}bg-primary text-white dark:bg-darkmode-primary dark:text-darkmode-body{{ else }}bg-white dark:bg-darkmode-body border border-border dark:border-darkmode-border hover:border-primary dark:hover:border-darkmode-primary hover:bg-primary hover:text-white dark:hover:bg-darkmode-primary dark:hover:text-darkmode-body{{ end }} transition-all duration-300">
              <i class="fas fa-lightbulb mr-2 text-xs"></i>
              <span class="font-medium">{{ .Page.Title }}</span>
              <span class="ml-2 text-xs {{ if eq $.Data.Term .Page.Title }}bg-white/20 text-white{{ else }}bg-primary/10 dark:bg-darkmode-primary/10 text-primary dark:text-darkmode-primary{{ end }} px-2 py-1 rounded-full">
                {{ len $items }}
              </span>
            </a>
          {{ end }}
        </div>
      </div>
    </div>
    {{ end }}

    <!-- Content Grid -->
    <div class="row">
      {{ $paginator := .Paginate .Data.Pages 9 }}
      {{ if $paginator.Pages }}
        {{ range $paginator.Pages }}
        <div class="lg:col-4 md:col-6 mb-8">
          {{ partial "components/til-card" . }}
        </div>
        {{ end }}
      {{ else }}
        <div class="col-12 text-center py-16">
          <div class="mb-6">
            <div class="inline-flex items-center justify-center w-24 h-24 bg-primary/10 dark:bg-darkmode-primary/10 rounded-full mb-4">
              <i class="fas fa-lightbulb text-4xl text-primary dark:text-darkmode-primary opacity-50"></i>
            </div>
          </div>
          <h3 class="text-2xl font-bold mb-4 text-dark dark:text-darkmode-dark">No Entries Yet</h3>
          <p class="text-lg text-text dark:text-darkmode-text mb-8">This category doesn't have any TIL entries yet. Check back later or explore other categories!</p>
          <div class="flex justify-center space-x-4">
            <a href="/til_categories/" class="btn btn-outline-primary">Browse All Categories</a>
            <a href="/til/" class="btn btn-primary">View All TIL</a>
          </div>
        </div>
      {{ end }}
    </div>

    <!-- Pagination -->
    {{ if gt $paginator.TotalPages 1 }}
    <div class="mt-12">
      {{ partial "components/pagination" . }}
    </div>
    {{ end }}

    <!-- Related Categories -->
    {{ if gt (len site.Taxonomies.til_categories) 1 }}
    <div class="mt-16 pt-8 border-t border-border dark:border-darkmode-border">
      <div class="text-center mb-8">
        <h2 class="text-2xl font-bold mb-4 text-dark dark:text-darkmode-dark">Explore Other Categories</h2>
        <p class="text-text dark:text-darkmode-text">Discover more domains and subjects</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {{ range first 6 (shuffle site.Taxonomies.til_categories.Alphabetical) }}
          {{ if ne .Page.Title $.Title }}
          <div class="bg-theme-light dark:bg-darkmode-theme-light rounded-xl p-6 border border-border dark:border-darkmode-border hover:border-primary dark:hover:border-darkmode-primary transition-all duration-300 hover:shadow-lg group">
            <a href="{{ .Page.RelPermalink }}" class="block">
              <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-primary/10 dark:bg-darkmode-primary/10 rounded-full flex items-center justify-center mr-3 group-hover:bg-primary group-hover:text-white dark:group-hover:bg-darkmode-primary dark:group-hover:text-darkmode-body transition-colors">
                  <i class="fas fa-lightbulb"></i>
                </div>
                <div class="flex-1">
                  <h3 class="font-bold text-dark dark:text-darkmode-dark group-hover:text-primary dark:group-hover:text-darkmode-primary transition-colors">{{ .Page.Title }}</h3>
                  <div class="text-sm text-text dark:text-darkmode-text">{{ .Count }} {{ if eq .Count 1 }}entry{{ else }}entries{{ end }}</div>
                </div>
              </div>
            </a>
          </div>
          {{ end }}
        {{ end }}
      </div>
    </div>
    {{ end }}
  </div>
</section>
{{ end }}
