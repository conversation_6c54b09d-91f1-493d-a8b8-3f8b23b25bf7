// Modern image slider styles
#image-slider {
  width: 100%;
  overflow: hidden;
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease;
  
  &:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15), 0 10px 20px rgba(0, 0, 0, 0.08);
  }
  
  #slider-track {
    display: flex;
    transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
  }
  
  .slide-item {
    min-width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    background: linear-gradient(to bottom, rgba(255,255,255,0.03), rgba(0,0,0,0.02));
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.05) 50%, rgba(255,255,255,0) 100%);
      transform: translateX(-100%);
      transition: transform 1.2s ease-in-out;
      pointer-events: none;
      z-index: 2;
    }
    
    &.active::before {
      transform: translateX(100%);
    }
    
    img {
      max-height: 500px;
      object-fit: contain;
      max-width: 100%;
      border-radius: 0.5rem;
      filter: drop-shadow(0 5px 10px rgba(0, 0, 0, 0.05));
      transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      transform-origin: center;
      will-change: transform, opacity, filter;
      
      &.entering {
        animation: scaleIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
      }
    }
  }
  
  @keyframes scaleIn {
    0% {
      opacity: 0.5;
      transform: scale(0.95);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  // Dark mode adjustments
  .dark & {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25), 0 5px 15px rgba(0, 0, 0, 0.15);
    
    &:hover {
      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3), 0 10px 20px rgba(0, 0, 0, 0.2);
    }
    
    .slide-item {
      background: linear-gradient(to bottom, rgba(255,255,255,0.01), rgba(0,0,0,0.05));
    }
  }
}
