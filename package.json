{"name": "<PERSON><PERSON>h", "description": "hugo tailwindcss boilerplate", "version": "1.9.7", "license": "MIT", "author": "su<PERSON><PERSON>.kasti", "scripts": {"dev": "hugo server", "build": "hugo --gc --minify --templateMetrics --templateMetricsHints --forceSyncStatic", "preview": "hugo server --disableFastRender --navigateToChanged --templateMetrics --templateMetricsHints --watch --forceSyncStatic -e production --minify", "dev:example": "cd exampleSite; hugo server", "build:example": "cd exampleSite; hugo --gc --minify --templateMetrics --templateMetricsHints --forceSyncStatic", "preview:example": "cd exampleSite; hugo server --disableFastRender --navigateToChanged --templateMetrics --templateMetricsHints --watch --forceSyncStatic -e production --minify", "update-modules": "node ./scripts/clearModules.js && hugo mod clean --all && hugo mod get -u ./... && hugo mod tidy", "remove-darkmode": "node ./scripts/removeDarkmode.js && yarn format", "project-setup": "node ./scripts/projectSetup.js", "theme-setup": "node ./scripts/themeSetup.js", "update-theme": "node ./scripts/themeUpdate.js", "format": "prettier -w ."}, "devDependencies": {"@fullhuman/postcss-purgecss": "^5.0.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.20", "chokidar": "^4.0.3", "concurrently": "^9.1.2", "postcss": "^8.4.47", "postcss-cli": "^10.1.0", "prettier": "^3.1.0", "prettier-plugin-go-template": "0.0.15", "prettier-plugin-tailwindcss": "^0.5.7", "tailwind-bootstrap-grid": "^5.0.1", "tailwindcss": "^3.3.5"}}